{"rustc": 15192701991585703681, "features": "[\"default\"]", "declared_features": "[\"asm\", \"default\", \"parallel\", \"rayon\", \"std\"]", "target": 4360302069253712615, "profile": 15657897354478470176, "path": 688713102004489986, "deps": [[477150410136574819, "ark_ff_macros", false, 11871343674600560219], [2932480923465029663, "zeroize", false, 9612811431494144393], [5157631553186200874, "num_traits", false, 3289968637329097523], [11903278875415370753, "itertools", false, 7449804240949708315], [12528732512569713347, "num_bigint", false, 15716811118992671935], [13859769749131231458, "derivative", false, 17302005347069451014], [15179503056858879355, "ark_std", false, 8756813108981474114], [16925068697324277505, "ark_serialize", false, 10772044682089235964], [17475753849556516473, "digest", false, 6734403052584998277], [17605717126308396068, "paste", false, 9493078349531651529], [17996237327373919127, "ark_ff_asm", false, 7691998660914532520]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ark-ff-ca778fb5ac02c8e8\\dep-lib-ark_ff", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}