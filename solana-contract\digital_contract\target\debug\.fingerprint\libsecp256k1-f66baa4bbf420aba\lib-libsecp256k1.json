{"rustc": 15192701991585703681, "features": "[\"default\", \"hmac\", \"hmac-drbg\", \"sha2\", \"static-context\", \"std\", \"typenum\"]", "declared_features": "[\"default\", \"hmac\", \"hmac-drbg\", \"lazy-static-context\", \"lazy_static\", \"sha2\", \"static-context\", \"std\", \"typenum\"]", "target": 3229137391415082075, "profile": 15657897354478470176, "path": 8322104959381727546, "deps": [[326483822194815791, "hmac_drbg", false, 10603150303565875867], [4731167174326621189, "rand", false, 625387790562462049], [6374421995994392543, "digest", false, 6729564516131487030], [9529943735784919782, "arrayref", false, 2501340853643278048], [9689903380558560274, "serde", false, 10994897986356711680], [10697153736615144157, "build_script_build", false, 7925950483642057406], [11472355562936271783, "sha2", false, 9238266262815500120], [13443824959912985638, "libsecp256k1_core", false, 10184247724115951618], [17001665395952474378, "typenum", false, 7136318697478753220], [17072468807347166763, "base64", false, 8732967385012423206]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\libsecp256k1-f66baa4bbf420aba\\dep-lib-libsecp256k1", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}