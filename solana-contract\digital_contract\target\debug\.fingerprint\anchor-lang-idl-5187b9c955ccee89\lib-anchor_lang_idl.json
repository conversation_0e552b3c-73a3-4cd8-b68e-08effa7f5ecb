{"rustc": 15192701991585703681, "features": "[\"convert\", \"heck\", \"sha2\"]", "declared_features": "[\"build\", \"convert\", \"heck\", \"regex\", \"sha2\"]", "target": 13617976458226247918, "profile": 2225463790103693989, "path": 17005381855164985150, "deps": [[9689903380558560274, "serde", false, 17984536245890465524], [9857275760291862238, "sha2", false, 6116041893124337032], [13625485746686963219, "anyhow", false, 15944183888037737653], [15367738274754116744, "serde_json", false, 1504575052465001626], [16131248048418321657, "heck", false, 9889794495378483582], [17037804673887881428, "anchor_lang_idl_spec", false, 11060869603455851270]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\anchor-lang-idl-5187b9c955ccee89\\dep-lib-anchor_lang_idl", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}