# Development Environment Configuration
NODE_ENV=development
PORT=3001

# Database Configuration
MONGO_URI=******************************************************************************
REDIS_URL=redis://localhost:6379

# Solana Configuration - DEVELOPMENT
SOLANA_CLUSTER=devnet
SOLANA_RPC_URL=https://api.devnet.solana.com

# Platform Fee Configuration (Development keypair)
# Generate a new keypair for development: solana-keygen new
PLATFORM_FEE_RECIPIENT_PRIVATE_KEY=[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64]

# Solana Program Configuration
VITE_PROGRAM_ID=7ow2v1f2EFNWQAVwn6aXih3kuXBP5FAX9ni48Uqva1LK

# Email Configuration (Development)
RESEND_API_KEY=your_development_resend_api_key_here

# Security Secrets (Development - use simple values)
JWT_SECRET=dev_jwt_secret_change_in_production
SESSION_SECRET=dev_session_secret_change_in_production

# Frontend Configuration
VITE_API_URL=http://localhost:3001
VITE_BACKEND_URL=http://localhost:3001
VITE_SOLANA_CLUSTER=devnet

# Development Features
ENABLE_SWAGGER=true
ENABLE_DEBUG_ROUTES=true
ENABLE_REQUEST_LOGGING=true
LOG_LEVEL=debug

# CORS Configuration
CORS_ORIGIN=http://localhost:5173

# Development Analytics (Optional)
VITE_GA_TRACKING_ID=

# Development Monitoring
ENABLE_PERFORMANCE_MONITORING=false
