cargo:rustc-check-cfg=cfg(blake3_sse2_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_sse2_rust, values(none()))
cargo:rustc-check-cfg=cfg(blake3_sse41_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_sse41_rust, values(none()))
cargo:rustc-check-cfg=cfg(blake3_avx2_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_avx2_rust, values(none()))
cargo:rustc-check-cfg=cfg(blake3_avx512_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_neon, values(none()))
cargo:rustc-check-cfg=cfg(blake3_wasm32_simd, values(none()))
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rerun-if-env-changed=CARGO_FEATURE_NO_NEON
OUT_DIR = Some(\\?\C:\Users\<USER>\solana-contract\solana-contract\digital_contract\target\debug\build\blake3-7e06687b447f9fc4\out)
TARGET = Some(x86_64-pc-windows-msvc)
VCINSTALLDIR = None
VSTEL_MSBuildProjectFullPath = None
VSCMD_ARG_VCVARS_SPECTRE = None
WindowsSdkDir = None
WindowsSDKVersion = None
LIB = None
PATH = Some(\\?\C:\Users\<USER>\solana-contract\solana-contract\digital_contract\target\debug\deps;\\?\C:\Users\<USER>\solana-contract\solana-contract\digital_contract\target\debug;C:\Users\<USER>\.rustup\toolchains\nightly-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.avm\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\Downloads\solana-release-x86_64-pc-windows-msvc\solana-release\bin;C:\Program Files\Java\openlogic-openjdk-21.0.5+11-windows-x64;C:\Program Files\Java\openlogic-openjdk-21.0.5+11-windows-x64\bin;C:\Program Files\Microsoft\Azure Functions Core Tools\;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Users\<USER>\AppData\Local\Programs\Python\Python39\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python39\;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\Downloads\Python-3.11.11\Python-3.11.11\Scripts\;C:\Users\<USER>\Downloads\Python-3.11.11\Python-3.11.11;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\.flutter\flutter\bin;C:\Users\<USER>\Downloads\solana-release-x86_64-pc-windows-msvc\solana-release\bin;C:\Program Files\Java\openlogic-openjdk-21.0.5+11-windows-x64\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\Downloads\fnm-windows;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.cache\lm-studio\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\Scripts;'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\Scripts;C:\Users\<USER>\AppData\Local\Programs\Windsurf12\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;C:\Users\<USER>\.cargo\bin;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Users\<USER>\.rustup\toolchains\nightly-x86_64-pc-windows-msvc\bin)
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
CC_x86_64-pc-windows-msvc = None
CC_x86_64_pc_windows_msvc = None
HOST_CC = None
CC = None
OUT_DIR = Some(\\?\C:\Users\<USER>\solana-contract\solana-contract\digital_contract\target\debug\build\blake3-7e06687b447f9fc4\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:warning=Compiler family detection failed due to error: ToolExecError: command did not execute successfully (status code exit code: 2): "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX64\\x64\\cl.exe" "-E" "\\\\?\\C:\\Users\\<USER>\\solana-contract\\solana-contract\\digital_contract\\target\\debug\\build\\blake3-7e06687b447f9fc4\\out\\9607405455186677945detect_compiler_family.c"
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,lahfsahf,sse,sse2,sse3,x87)
HOST = Some(x86_64-pc-windows-msvc)
CFLAGS = None
HOST_CFLAGS = None
CFLAGS_x86_64_pc_windows_msvc = None
CFLAGS_x86_64-pc-windows-msvc = None
OPT_LEVEL = Some(0)
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,lahfsahf,sse,sse2,sse3,x87)
DEBUG = Some(true)
CFLAGS = None
HOST_CFLAGS = None
CFLAGS_x86_64_pc_windows_msvc = None
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some(--cfgprocmacro2_semver_exempt)
cargo:warning=The C compiler "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX64\\x64\\cl.exe" does not support /arch:AVX512.
cargo:rerun-if-env-changed=BLAKE3_CI
cargo:rerun-if-env-changed=CARGO_FEATURE_PREFER_INTRINSICS
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rustc-cfg=blake3_sse2_ffi
cargo:rustc-cfg=blake3_sse41_ffi
cargo:rustc-cfg=blake3_avx2_ffi
OUT_DIR = Some(\\?\C:\Users\<USER>\solana-contract\solana-contract\digital_contract\target\debug\build\blake3-7e06687b447f9fc4\out)
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-pc-windows-msvc)
VCINSTALLDIR = None
VSTEL_MSBuildProjectFullPath = None
VSCMD_ARG_VCVARS_SPECTRE = None
WindowsSdkDir = None
WindowsSDKVersion = None
LIB = None
PATH = Some(\\?\C:\Users\<USER>\solana-contract\solana-contract\digital_contract\target\debug\deps;\\?\C:\Users\<USER>\solana-contract\solana-contract\digital_contract\target\debug;C:\Users\<USER>\.rustup\toolchains\nightly-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Users\<USER>\.local\share\solana\install\active_release\bin;C:\Users\<USER>\.avm\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\Downloads\solana-release-x86_64-pc-windows-msvc\solana-release\bin;C:\Program Files\Java\openlogic-openjdk-21.0.5+11-windows-x64;C:\Program Files\Java\openlogic-openjdk-21.0.5+11-windows-x64\bin;C:\Program Files\Microsoft\Azure Functions Core Tools\;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Users\<USER>\AppData\Local\Programs\Python\Python39\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python39\;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\Downloads\Python-3.11.11\Python-3.11.11\Scripts\;C:\Users\<USER>\Downloads\Python-3.11.11\Python-3.11.11;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\.flutter\flutter\bin;C:\Users\<USER>\Downloads\solana-release-x86_64-pc-windows-msvc\solana-release\bin;C:\Program Files\Java\openlogic-openjdk-21.0.5+11-windows-x64\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\Downloads\fnm-windows;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.cache\lm-studio\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\Scripts;'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\Scripts;C:\Users\<USER>\AppData\Local\Programs\Windsurf12\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;C:\Users\<USER>\.cargo\bin;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Users\<USER>\.rustup\toolchains\nightly-x86_64-pc-windows-msvc\bin)
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
CC_x86_64-pc-windows-msvc = None
CC_x86_64_pc_windows_msvc = None
HOST_CC = None
CC = None
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,lahfsahf,sse,sse2,sse3,x87)
DEBUG = Some(true)
CFLAGS = None
HOST_CFLAGS = None
CFLAGS_x86_64_pc_windows_msvc = None
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some(--cfgprocmacro2_semver_exempt)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
 Assembling: c/blake3_sse2_x86-64_windows_msvc.asm
 Assembling: c/blake3_sse41_x86-64_windows_msvc.asm
 Assembling: c/blake3_avx2_x86-64_windows_msvc.asm
exit code: 0
exit code: 0
exit code: 0
AR_x86_64-pc-windows-msvc = None
AR_x86_64_pc_windows_msvc = None
HOST_AR = None
AR = None
ARFLAGS = None
HOST_ARFLAGS = None
ARFLAGS_x86_64_pc_windows_msvc = None
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-lib=static=blake3_sse2_sse41_avx2_assembly
cargo:rustc-link-search=native=\\?\C:\Users\<USER>\solana-contract\solana-contract\digital_contract\target\debug\build\blake3-7e06687b447f9fc4\out
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rerun-if-env-changed=CARGO_FEATURE_NEON
cargo:rerun-if-env-changed=CARGO_FEATURE_NO_NEON
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rerun-if-env-changed=CC
cargo:rerun-if-env-changed=CFLAGS
cargo:rerun-if-changed=c\.gitignore
cargo:rerun-if-changed=c\blake3-config.cmake.in
cargo:rerun-if-changed=c\blake3.c
cargo:rerun-if-changed=c\blake3.h
cargo:rerun-if-changed=c\blake3_avx2.c
cargo:rerun-if-changed=c\blake3_avx2_x86-64_unix.S
cargo:rerun-if-changed=c\blake3_avx2_x86-64_windows_gnu.S
cargo:rerun-if-changed=c\blake3_avx2_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c\blake3_avx512.c
cargo:rerun-if-changed=c\blake3_avx512_x86-64_unix.S
cargo:rerun-if-changed=c\blake3_avx512_x86-64_windows_gnu.S
cargo:rerun-if-changed=c\blake3_avx512_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c\blake3_dispatch.c
cargo:rerun-if-changed=c\blake3_impl.h
cargo:rerun-if-changed=c\blake3_neon.c
cargo:rerun-if-changed=c\blake3_portable.c
cargo:rerun-if-changed=c\blake3_sse2.c
cargo:rerun-if-changed=c\blake3_sse2_x86-64_unix.S
cargo:rerun-if-changed=c\blake3_sse2_x86-64_windows_gnu.S
cargo:rerun-if-changed=c\blake3_sse2_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c\blake3_sse41.c
cargo:rerun-if-changed=c\blake3_sse41_x86-64_unix.S
cargo:rerun-if-changed=c\blake3_sse41_x86-64_windows_gnu.S
cargo:rerun-if-changed=c\blake3_sse41_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c\blake3_tbb.cpp
cargo:rerun-if-changed=c\cmake
cargo:rerun-if-changed=c\CMakeLists.txt
cargo:rerun-if-changed=c\CMakePresets.json
cargo:rerun-if-changed=c\dependencies
cargo:rerun-if-changed=c\example.c
cargo:rerun-if-changed=c\example_tbb.c
cargo:rerun-if-changed=c\libblake3.pc.in
cargo:rerun-if-changed=c\main.c
cargo:rerun-if-changed=c\Makefile.testing
cargo:rerun-if-changed=c\README.md
cargo:rerun-if-changed=c\test.py
