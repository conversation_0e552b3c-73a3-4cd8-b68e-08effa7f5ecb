[toolchain]
anchor_version = "0.30.1"

[features]
seeds = false
skip-lint = false

[programs.devnet]
digital_contract = "7ow2v1f2EFNWQAVwn6aXih3kuXBP5FAX9ni48Uqva1LK"

[programs.testnet]
digital_contract = "7ow2v1f2EFNWQAVwn6aXih3kuXBP5FAX9ni48Uqva1LK"

[programs.localnet]
digital_contract = "7ow2v1f2EFNWQAVwn6aXih3kuXBP5FAX9ni48Uqva1LK"

[registry]
url = "https://api.apr.dev"

[provider]
cluster = "devnet"
wallet = "../../backend/wallet.json"

[scripts]
test = "yarn run ts-mocha -p ./tsconfig.json -t 1000000 tests/**/*.ts"
