{"rustc": 15192701991585703681, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 15657897354478470176, "path": 10159242958768791439, "deps": [[1573238666360410412, "rand_chacha", false, 14923206905624883683], [18130209639506977569, "rand_core", false, 12981093998459481283]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-0667662fcea858f4\\dep-lib-rand", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}