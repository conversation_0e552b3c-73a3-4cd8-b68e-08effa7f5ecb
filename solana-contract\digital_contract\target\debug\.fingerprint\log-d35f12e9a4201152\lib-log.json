{"rustc": 15192701991585703681, "features": "[\"std\"]", "declared_features": "[\"kv\", \"kv_serde\", \"kv_std\", \"kv_sval\", \"kv_unstable\", \"kv_unstable_serde\", \"kv_unstable_std\", \"kv_unstable_sval\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"serde\", \"std\", \"sval\", \"sval_ref\", \"value-bag\"]", "target": 6550155848337067049, "profile": 15657897354478470176, "path": 5211172694815459584, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\log-d35f12e9a4201152\\dep-lib-log", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}