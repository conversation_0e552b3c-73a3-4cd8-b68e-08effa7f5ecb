{"rustc": 15192701991585703681, "features": "[]", "declared_features": "[\"extra-traits\"]", "target": 4856214846215393392, "profile": 1300506145316312068, "path": 9432325843203867935, "deps": [[3060637413840920116, "proc_macro2", false, 12160729066652882429], [4974441333307933176, "syn", false, 18070174274004025583], [5986029879202738730, "log", false, 724750081794385444], [13336078982182647123, "<PERSON><PERSON>", false, 1333508763160086347], [14299170049494554845, "wasm_bindgen_shared", false, 8737600180087591340], [17990358020177143287, "quote", false, 7684078110656465628]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wasm-bindgen-backend-baf80a54e2a2147c\\dep-lib-wasm_bindgen_backend", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}