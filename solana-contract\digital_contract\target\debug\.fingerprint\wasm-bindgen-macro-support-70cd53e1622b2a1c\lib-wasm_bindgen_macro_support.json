{"rustc": 15192701991585703681, "features": "[]", "declared_features": "[\"extra-traits\", \"strict-macro\"]", "target": 17930477452216118438, "profile": 1300506145316312068, "path": 2934864056184501111, "deps": [[3060637413840920116, "proc_macro2", false, 12160729066652882429], [4974441333307933176, "syn", false, 18070174274004025583], [14299170049494554845, "wasm_bindgen_shared", false, 8737600180087591340], [14372503175394433084, "wasm_bindgen_backend", false, 96494443362612158], [17990358020177143287, "quote", false, 7684078110656465628]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wasm-bindgen-macro-support-70cd53e1622b2a1c\\dep-lib-wasm_bindgen_macro_support", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}