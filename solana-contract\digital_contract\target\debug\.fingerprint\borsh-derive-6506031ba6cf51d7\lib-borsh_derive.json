{"rustc": 15192701991585703681, "features": "[\"default\", \"schema\"]", "declared_features": "[\"default\", \"force_exhaustive_checks\", \"schema\"]", "target": 18019366223131144178, "profile": 2225463790103693989, "path": 4031976182627371360, "deps": [[3060637413840920116, "proc_macro2", false, 12160729066652882429], [3722963349756955755, "once_cell", false, 2118808874383983019], [4974441333307933176, "syn", false, 18070174274004025583], [15203748914246919255, "proc_macro_crate", false, 12905658267439694595], [17990358020177143287, "quote", false, 7684078110656465628]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\borsh-derive-6506031ba6cf51d7\\dep-lib-borsh_derive", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}