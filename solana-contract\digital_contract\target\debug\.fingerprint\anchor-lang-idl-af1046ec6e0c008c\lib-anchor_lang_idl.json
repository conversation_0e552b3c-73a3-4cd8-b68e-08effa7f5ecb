{"rustc": 15192701991585703681, "features": "[\"build\", \"regex\"]", "declared_features": "[\"build\", \"convert\", \"heck\", \"regex\", \"sha2\"]", "target": 13617976458226247918, "profile": 15657897354478470176, "path": 17005381855164985150, "deps": [[9451456094439810778, "regex", false, 14465251934838442918], [9689903380558560274, "serde", false, 10994897986356711680], [13625485746686963219, "anyhow", false, 15944183888037737653], [15367738274754116744, "serde_json", false, 4881223110466591647], [17037804673887881428, "anchor_lang_idl_spec", false, 10528035277387647726]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\anchor-lang-idl-af1046ec6e0c008c\\dep-lib-anchor_lang_idl", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}