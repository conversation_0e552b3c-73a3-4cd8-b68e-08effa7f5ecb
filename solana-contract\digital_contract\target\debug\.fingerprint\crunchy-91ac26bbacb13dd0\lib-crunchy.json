{"rustc": 15192701991585703681, "features": "[\"default\", \"limit_128\"]", "declared_features": "[\"default\", \"limit_1024\", \"limit_128\", \"limit_2048\", \"limit_256\", \"limit_512\", \"limit_64\", \"std\"]", "target": 9963013543797884993, "profile": 15657897354478470176, "path": 10336972970552275658, "deps": [[5148925301303650630, "build_script_build", false, 18221759141911974776]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\crunchy-91ac26bbacb13dd0\\dep-lib-crunchy", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}