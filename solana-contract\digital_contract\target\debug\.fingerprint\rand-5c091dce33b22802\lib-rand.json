{"rustc": 15192701991585703681, "features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"log\", \"nightly\", \"packed_simd\", \"rand_pcg\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"stdweb\", \"wasm-bindgen\"]", "target": 8827111241893198906, "profile": 15657897354478470176, "path": 14603826109901049287, "deps": [[1333041802001714747, "rand_chacha", false, 11298413927529805291], [1740877332521282793, "rand_core", false, 6869954074633227312], [5170503507811329045, "getrandom_package", false, 5510387364447328479]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-5c091dce33b22802\\dep-lib-rand", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}