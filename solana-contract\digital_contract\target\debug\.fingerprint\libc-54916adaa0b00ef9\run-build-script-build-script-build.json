{"rustc": 15192701991585703681, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4684437522915235464, "build_script_build", false, 13283366027212859939]], "local": [{"RerunIfChanged": {"output": "debug\\build\\libc-54916adaa0b00ef9\\output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_FREEBSD_VERSION", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_MUSL_V1_2_3", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_LINUX_TIME_BITS64", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_GNU_FILE_OFFSET_BITS", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_GNU_TIME_BITS", "val": null}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 0, "compile_kind": 0}