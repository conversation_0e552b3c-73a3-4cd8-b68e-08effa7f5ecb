{"rustc": 15192701991585703681, "features": "[\"chinese-simplified\", \"chinese-traditional\", \"default\", \"french\", \"italian\", \"japanese\", \"korean\", \"spanish\"]", "declared_features": "[\"chinese-simplified\", \"chinese-traditional\", \"default\", \"french\", \"italian\", \"japanese\", \"korean\", \"spanish\"]", "target": 12173490326672380777, "profile": 15657897354478470176, "path": 12849467776818581225, "deps": [[2932480923465029663, "zeroize", false, 9612811431494144393], [3722963349756955755, "once_cell", false, 2118808874383983019], [4731167174326621189, "rand", false, 625387790562462049], [5376060773002235395, "unicode_normalization", false, 1604851474731117216], [5392525048748667223, "hmac", false, 4198622814004819341], [6356980012369731934, "pbkdf2", false, 6733762371498193054], [8008191657135824715, "thiserror", false, 9296780215146130139], [11472355562936271783, "sha2", false, 9238266262815500120], [13625485746686963219, "anyhow", false, 15944183888037737653], [16055916053474393816, "rustc_hash", false, 1215731765460193333]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tiny-bip39-6e400a0af1cbb91d\\dep-lib-bip39", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}