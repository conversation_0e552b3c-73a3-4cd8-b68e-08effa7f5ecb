# Backend Environment Configuration - Local Development Template
# Copy this file to .env.local and fill in your actual values
# DO NOT COMMIT .env.local TO VERSION CONTROL

# Application Environment
NODE_ENV=development
PORT=3001

# Database Configuration
MONGO_URI=******************************************************************************
REDIS_URL=redis://localhost:6379

# Solana Configuration - DEVELOPMENT
SOLANA_CLUSTER=devnet
SOLANA_RPC_URL=https://api.devnet.solana.com

# Platform Fee Configuration (Development keypair)
# Generate a new keypair for development: solana-keygen new
# Replace with your actual development keypair
PLATFORM_FEE_RECIPIENT_PRIVATE_KEY=[your,development,private,key,array,here]

# Solana Program Configuration
PROGRAM_ID=7ow2v1f2EFNWQAVwn6aXih3kuXBP5FAX9ni48Uqva1LK

# Email Configuration (Development)
# Get your API key from https://resend.com/api-keys
RESEND_API_KEY=re_your_development_resend_api_key_here

# Security Secrets (Generate with: openssl rand -hex 32)
JWT_SECRET=your_secure_jwt_secret_here_64_characters_minimum
SESSION_SECRET=your_secure_session_secret_here_64_characters_minimum

# CORS Configuration
CORS_ORIGIN=http://localhost:5173
FRONTEND_URL=http://localhost:5173

# Development Features
ENABLE_SWAGGER=true
ENABLE_DEBUG_ROUTES=true
ENABLE_REQUEST_LOGGING=true
LOG_LEVEL=debug

# Development Monitoring
ENABLE_PERFORMANCE_MONITORING=false
ENABLE_ERROR_TRACKING=false

# Rate Limiting (Relaxed for development)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# WebSocket Configuration
WEBSOCKET_CORS_ORIGIN=http://localhost:5173
WEBSOCKET_TRANSPORTS=websocket,polling

# File Upload Configuration
MAX_FILE_SIZE=50mb
UPLOAD_DIR=./uploads

# IPFS Configuration (Optional)
IPFS_HOST=localhost
IPFS_PORT=5001
IPFS_PROTOCOL=http

# Development Database Settings
DB_DEBUG=true
DB_SLOW_QUERY_THRESHOLD=100

# Cache Configuration
CACHE_TTL=300
CACHE_MAX_SIZE=100

# Development Email Settings
EMAIL_FROM=noreply@localhost
EMAIL_REPLY_TO=support@localhost

# Solana Development Settings
SOLANA_COMMITMENT=confirmed
SOLANA_TIMEOUT=30000
SOLANA_MAX_RETRIES=3

# Development Wallet Settings (Optional)
DEV_WALLET_PRIVATE_KEY=
DEV_WALLET_PUBLIC_KEY=

# Local Development URLs
API_BASE_URL=http://localhost:3001
FRONTEND_BASE_URL=http://localhost:5173

# Development Security Settings
DISABLE_RATE_LIMITING=false
ALLOW_INSECURE_CONNECTIONS=true
SKIP_SSL_VERIFICATION=true

# Monitoring and Health Check
HEALTH_CHECK_INTERVAL=30000
ENABLE_METRICS=false

# Development Contract Settings
CONTRACT_PROGRAM_ID=4bmYTgHAoYfBBwoELVqUzc9n8DTfFvtt7CodYq78wzir
CONTRACT_DEPLOYMENT_NETWORK=devnet

# Local Development Flags
ENABLE_MOCK_SERVICES=false
SKIP_BLOCKCHAIN_VALIDATION=false
ENABLE_TEST_ROUTES=true
