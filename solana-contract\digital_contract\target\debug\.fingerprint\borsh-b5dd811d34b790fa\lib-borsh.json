{"rustc": 15192701991585703681, "features": "[\"borsh-derive\", \"default\", \"derive\", \"std\", \"unstable__schema\"]", "declared_features": "[\"ascii\", \"borsh-derive\", \"bson\", \"bytes\", \"de_strict_order\", \"default\", \"derive\", \"hashbrown\", \"indexmap\", \"rc\", \"std\", \"unstable__schema\"]", "target": 4760962088884618199, "profile": 15657897354478470176, "path": 7164427933930048390, "deps": [[5176760276063461045, "borsh_derive", false, 6715418603148027042], [6203123018298125816, "build_script_build", false, 11866447105427549780]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\borsh-b5dd811d34b790fa\\dep-lib-borsh", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}