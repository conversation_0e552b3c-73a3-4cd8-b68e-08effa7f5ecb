{"rustc": 15192701991585703681, "features": "[\"alloc\", \"default\", \"serde\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"avx2_backend\", \"default\", \"fiat-crypto\", \"fiat_u32_backend\", \"fiat_u64_backend\", \"nightly\", \"packed_simd\", \"serde\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 4744499769514376500, "profile": 15657897354478470176, "path": 3727205039737180663, "deps": [[1740877332521282793, "rand_core", false, 6869954074633227312], [2932480923465029663, "zeroize", false, 9612811431494144393], [3712811570531045576, "byteorder", false, 3590068255320480469], [6374421995994392543, "digest", false, 6729564516131487030], [9689903380558560274, "serde", false, 10994897986356711680], [17003143334332120809, "subtle", false, 13807153549056100879]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\curve25519-dalek-7762ad28a7c94b79\\dep-lib-curve25519_dalek", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}