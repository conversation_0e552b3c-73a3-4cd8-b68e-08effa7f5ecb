{"rustc": 15192701991585703681, "features": "[]", "declared_features": "[\"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 6875603382767429092, "profile": 1300506145316312068, "path": 4127932450534697790, "deps": [[2589611628054203282, "wasm_bindgen_macro_support", false, 2931800313815623183], [17990358020177143287, "quote", false, 7684078110656465628]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wasm-bindgen-macro-07e5a489f7b4c737\\dep-lib-wasm_bindgen_macro", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}