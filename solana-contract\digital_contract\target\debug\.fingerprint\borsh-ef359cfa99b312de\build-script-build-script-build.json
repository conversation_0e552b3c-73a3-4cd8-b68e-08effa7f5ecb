{"rustc": 15192701991585703681, "features": "[\"borsh-derive\", \"default\", \"derive\", \"std\", \"unstable__schema\"]", "declared_features": "[\"ascii\", \"borsh-derive\", \"bson\", \"bytes\", \"de_strict_order\", \"default\", \"derive\", \"hashbrown\", \"indexmap\", \"rc\", \"std\", \"unstable__schema\"]", "target": 17883862002600103897, "profile": 2225463790103693989, "path": 831764823222682359, "deps": [[1884099982326826527, "cfg_aliases", false, 311854304060343599]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\borsh-ef359cfa99b312de\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}