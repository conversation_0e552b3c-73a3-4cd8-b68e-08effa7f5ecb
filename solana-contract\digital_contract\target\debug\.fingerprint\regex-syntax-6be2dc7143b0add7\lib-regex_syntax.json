{"rustc": 15192701991585703681, "features": "[\"default\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"arbitrary\", \"default\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 742186494246220192, "profile": 15657897354478470176, "path": 8957722618831435973, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-syntax-6be2dc7143b0add7\\dep-lib-regex_syntax", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}