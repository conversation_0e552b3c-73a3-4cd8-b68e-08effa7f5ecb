{"rustc": 15192701991585703681, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 6374401459973044251, "path": 102755726551632908, "deps": [[2828590642173593838, "cfg_if", false, 9635675543248725535], [3722963349756955755, "once_cell", false, 2118808874383983019], [6946689283190175495, "build_script_build", false, 2528829993785526539], [7858942147296547339, "rustversion", false, 8982757480646108589], [11382113702854245495, "wasm_bindgen_macro", false, 7256152724783114872]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wasm-bindgen-d3531d92a4535496\\dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}