{"rustc": 15192701991585703681, "features": "[\"std\"]", "declared_features": "[\"bindgen\", \"compiler_builtins\", \"core\", \"dummy\", \"js-sys\", \"log\", \"rustc-dep-of-std\", \"std\", \"stdweb\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 17883862002600103897, "profile": 2225463790103693989, "path": 4099164611043584686, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\getrandom-3774c59a233908c6\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}