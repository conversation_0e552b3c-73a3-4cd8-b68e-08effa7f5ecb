{"rustc": 15192701991585703681, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"cb58\", \"check\", \"default\", \"sha2\", \"smallvec\", \"std\", \"tinyvec\"]", "target": 2243021261112611720, "profile": 2225463790103693989, "path": 12647280351296972153, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\bs58-570069d37b13c6dc\\dep-lib-bs58", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}