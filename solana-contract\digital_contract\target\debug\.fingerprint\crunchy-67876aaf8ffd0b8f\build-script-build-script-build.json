{"rustc": 15192701991585703681, "features": "[\"default\", \"limit_128\"]", "declared_features": "[\"default\", \"limit_1024\", \"limit_128\", \"limit_2048\", \"limit_256\", \"limit_512\", \"limit_64\", \"std\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 8396154683008448917, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\crunchy-67876aaf8ffd0b8f\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}