# Devnet Environment Configuration
VITE_SOLANA_CLUSTER=devnet
VITE_API_URL=http://localhost:3001
VITE_PROGRAM_ID=7ow2v1f2EFNWQAVwn6aXih3kuXBP5FAX9ni48Uqva1LK

# Backend Configuration
NODE_ENV=development
PORT=3001
SOLANA_CLUSTER=devnet
PLATFORM_FEE_RECIPIENT=BcSXav3jcBKRbN6woZsqPMJGYrS2MXwVEdtUx1ZzD9Xo

# MongoDB Configuration
MONGO_URI=mongodb+srv://contractDB:<EMAIL>/contractDB?retryWrites=true&w=majority

# Development flags
VITE_ENABLE_DEV_TOOLS=true
VITE_SHOW_NETWORK_INDICATOR=true
