{"rustc": 15192701991585703681, "features": "[]", "declared_features": "[\"base64\", \"default\", \"hmac\", \"include_simple\", \"parallel\", \"rand\", \"rand_core\", \"rayon\", \"sha2\", \"std\", \"subtle\", \"thread_rng\"]", "target": 13850014456683401616, "profile": 15657897354478470176, "path": 8386729649217436808, "deps": [[1854422166435677683, "crypto_mac", false, 11114303301899325890]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pbkdf2-4b57979642da00d1\\dep-lib-pbkdf2", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}