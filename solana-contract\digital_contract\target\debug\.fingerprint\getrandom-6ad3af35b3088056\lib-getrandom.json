{"rustc": 15192701991585703681, "features": "[\"std\"]", "declared_features": "[\"bindgen\", \"compiler_builtins\", \"core\", \"dummy\", \"js-sys\", \"log\", \"rustc-dep-of-std\", \"std\", \"stdweb\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 3140061874755240240, "profile": 15657897354478470176, "path": 1711664217108454120, "deps": [[2828590642173593838, "cfg_if", false, 9635675543248725535], [5170503507811329045, "build_script_build", false, 399897966660725105]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\getrandom-6ad3af35b3088056\\dep-lib-getrandom", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}