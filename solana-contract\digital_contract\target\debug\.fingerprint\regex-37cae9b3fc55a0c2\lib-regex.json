{"rustc": 15192701991585703681, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 15657897354478470176, "path": 12650040082789894044, "deps": [[555019317135488525, "regex_automata", false, 13925733178121751047], [2779309023524819297, "aho_corasick", false, 9012081414014271130], [9408802513701742484, "regex_syntax", false, 14251527911270656520], [15932120279885307830, "memchr", false, 5848625813634255247]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-37cae9b3fc55a0c2\\dep-lib-regex", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt"], "config": 2069994364910194474, "compile_kind": 0}